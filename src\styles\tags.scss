
.avue-tags {
  user-select: none;
  position: relative;
  padding: 0 10px;
  margin-bottom: 10px;
  box-sizing: border-box;
  border-top: 1px solid #f6f6f6;
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);

  .el-tabs--card > .el-tabs__header {
    margin: 0;
  }

  .el-tabs--card > .el-tabs__header .el-tabs__nav,
  .el-tabs--card > .el-tabs__header .el-tabs__item,
  .el-tabs--card > .el-tabs__header {
    border: none;
  }

  .el-tabs--card > .el-tabs__header .el-tabs__item:first-child {
    border-left-width: 1px
  }

  .el-tabs--card > .el-tabs__header .el-tabs__item {
    margin: 0 3px;
    height: 40px;
    line-height: 40px;
    font-size: 13px;
    font-weight: normal;
    color: #ccc;

    &.is-active {
      color: var(--el-color-primary);
      border-bottom: 3px solid var(--el-color-primary);
    }
  }

  .el-tabs__nav-prev,
  .el-tabs__nav-next {
    width: 20px;
    line-height: 40px;
    font-size: 18px;
    text-align: center;
  }

  &__box {
    position: relative;
    box-sizing: border-box;
    padding-right: 106px;
    width: 100%;

    .el-tabs__item {
      &:first-child {
        .is-icon-close {
          display: none;
        }
      }
    }
  }

  &__contentmenu {
    position: fixed;
    width: 120px;
    background-color: #fff;
    z-index: 1024;
    border-radius: 5px;
    box-shadow: 1px 2px 10px #ccc;

    .item {
      cursor: pointer;
      font-size: 14px;
      padding: 8px 20px 8px 15px;
      color: #606266;

      &:first-child {
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
      }

      &:last-child {
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;
      }

      &:hover {
        background-color: var(--el-color-primary);
        color: #fff;
      }
    }
  }

  &__menu {
    position: absolute !important;
    top: 3px;
    right: 0;
    padding: 1px 0 0 15px;
    box-sizing: border-box;
  }
}
