.theme-go {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1) !important;
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color) !important;
  .el-table .el-table__row td .cell {
    line-height: 32px;
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity));
  }
  .el-table tr th .cell {
    line-height: 25px;
    --tw-text-opacity: 1;
    color: rgb(55 65 81 / var(--tw-text-opacity));
  }


  .avue-sidebar {
    background:#fff !important;

    .el-menu-item, .el-sub-menu__title {
      i, span {
        color: #333;
      }

      &:hover {
        background: transparent;

        i, span {
          color: var(--el-color-primary);
        }
      }

      &.is-active {
        &:before {
          left: auto;
          right: 0;
        }

        background-color: #f0f6ff;

        i, span {
          color: var(--el-color-primary);
        }
      }
    }
  }

  .avue-logo {
    width:100%;
    height:50px;
    line-height: 50px;
    background:#fff;
    color:#333;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
  }

  .avue-top{
    height:50px;
    line-height: 50px;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
    .top-bar__left i, .top-bar__right i{
      line-height: 50px;
    }
    .el-menu--horizontal.el-menu{
      border:none
    }
    .el-menu-item{
      i,span{
        font-size: 15px;
      }
    }

    .top-bar__title{
      padding-left: 50px;
    }
  }
  .avue-main{
    background-color: rgb(249,250,251);
  }
  .avue-tags {
    margin: 5px 6px 8px 6px;
    padding-bottom: 2px;

    .el-tabs__nav-prev,
    .el-tabs__nav-next {
      width: 20px;
      line-height: 50px;
      font-size: 18px;
      text-align: center;
    }

    &__box {
      position: relative;
      box-sizing: border-box;
      padding-right: 70px;
      width: 100%;

      .el-tabs__item {
        &:first-child {
          .is-icon-close {
            display: none;
          }
        }
      }
    }

    .el-tabs__item {
      font-size: 14px !important;
      color: rgb(75,85,99) !important;
      font-weight: 500 !important;
      border: 1px solid rgb(205,218,214) !important;
      border-radius: 3px;
      height: 30px !important;
      line-height: 35px !important;
      margin: 8px 3px 8px 3px !important;

      &:hover {
        color: var(--el-color-primary) !important;
        border-color: var(--el-color-primary) !important;
      }
    }

    .is-active {
      color: var(--el-color-primary) !important;
      border-color: var(--el-color-primary) !important;
    }
  }

}
