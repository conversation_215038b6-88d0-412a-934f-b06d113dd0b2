<template>
  <div class="test-page">
    <div class="page-header">
      <h1>楼宇控制设备运行趋势图表组件测试</h1>
      <p>这是一个测试页面，用于验证设备运行趋势图表组件的功能</p>
    </div>
    
    <div class="chart-section">
      <div class="chart-wrapper">
        <h2>设备运行趋势图表</h2>
        <div class="chart-container">
          <Equipmentoperation />
        </div>
      </div>
    </div>
    
    <div class="info-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card header="功能特性">
            <ul>
              <li>✅ 支持周视图（柱状图）和日视图（折线图）切换</li>
              <li>✅ 四个数据系列：启动数、停止数、故障数、报警数</li>
              <li>✅ 响应式设计，适配不同屏幕尺寸</li>
              <li>✅ 交互式图表，鼠标悬停显示详细数据</li>
              <li>✅ 图例说明，区分四种设备运行状态</li>
              <li>✅ 数据单位显示（次数）</li>
            </ul>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card header="颜色方案">
            <div class="color-scheme">
              <div class="color-item">
                <div class="color-box startup"></div>
                <span>启动数 - 蓝色</span>
              </div>
              <div class="color-item">
                <div class="color-box shutdown"></div>
                <span>停止数 - 绿色</span>
              </div>
              <div class="color-item">
                <div class="color-box fault"></div>
                <span>故障数 - 橙色</span>
              </div>
              <div class="color-item">
                <div class="color-box alarm"></div>
                <span>报警数 - 红色</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <div class="data-section">
      <el-card header="数据格式示例">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="周数据格式" name="week">
            <pre><code>{{ weekDataExample }}</code></pre>
          </el-tab-pane>
          <el-tab-pane label="日数据格式" name="day">
            <pre><code>{{ dayDataExample }}</code></pre>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Equipmentoperation from '@/components/wel/equipmentoperation.vue'

const activeTab = ref('week')

const weekDataExample = `[
  { date: '周一', startup: 45, shutdown: 38, fault: 3, alarm: 2 },
  { date: '周二', startup: 52, shutdown: 41, fault: 2, alarm: 1 },
  { date: '周三', startup: 38, shutdown: 35, fault: 4, alarm: 3 },
  { date: '周四', startup: 48, shutdown: 42, fault: 1, alarm: 2 },
  { date: '周五', startup: 35, shutdown: 32, fault: 2, alarm: 1 },
  { date: '周六', startup: 28, shutdown: 25, fault: 1, alarm: 0 },
  { date: '周日', startup: 22, shutdown: 20, fault: 0, alarm: 1 }
]`

const dayDataExample = `[
  { hour: '00:00', startup: 2, shutdown: 1, fault: 0, alarm: 0 },
  { hour: '01:00', startup: 1, shutdown: 2, fault: 0, alarm: 0 },
  { hour: '02:00', startup: 1, shutdown: 1, fault: 0, alarm: 0 },
  // ... 其他小时数据
  { hour: '23:00', startup: 5, shutdown: 3, fault: 0, alarm: 0 }
]`
</script>

<style scoped lang="scss">
.test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  
  .page-header {
    text-align: center;
    margin-bottom: 30px;
    
    h1 {
      color: #303133;
      margin-bottom: 10px;
    }
    
    p {
      color: #606266;
      font-size: 16px;
    }
  }
  
  .chart-section {
    margin-bottom: 30px;
    
    .chart-wrapper {
      background: #fff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      
      h2 {
        color: #303133;
        margin-bottom: 20px;
        text-align: center;
      }
      
      .chart-container {
        height: 200px;
      }
    }
  }
  
  .info-section {
    margin-bottom: 30px;
    
    :deep(.el-card) {
      .el-card__header {
        background-color: #f5f7fa;
        font-weight: 600;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 8px;
          color: #606266;
        }
      }
    }
    
    .color-scheme {
      .color-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        
        .color-box {
          width: 20px;
          height: 20px;
          border-radius: 4px;
          margin-right: 12px;
          
          &.startup {
            background-color: #409EFF;
          }
          
          &.shutdown {
            background-color: #67C23A;
          }
          
          &.fault {
            background-color: #E6A23C;
          }
          
          &.alarm {
            background-color: #F56C6C;
          }
        }
        
        span {
          color: #606266;
          font-size: 14px;
        }
      }
    }
  }
  
  .data-section {
    :deep(.el-card) {
      .el-card__header {
        background-color: #f5f7fa;
        font-weight: 600;
      }
      
      pre {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 15px;
        margin: 0;
        overflow-x: auto;
        
        code {
          color: #495057;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .test-page {
    padding: 15px;
    
    .chart-section {
      .chart-wrapper {
        padding: 15px;
        
        .chart-container {
          height: 180px;
        }
      }
    }
  }
}
</style>
