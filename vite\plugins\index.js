import vue from '@vitejs/plugin-vue';
import process from 'vite-plugin-progress'
import vueDevTools from 'vite-plugin-vue-devtools'


import createAutoImport from './auto-import';
import createCompression from './compression';
import createSetupExtend from './setup-extend';

export default function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [vue(), vueDevTools(), process()];
  vitePlugins.push(createAutoImport());
  vitePlugins.push(createSetupExtend());
  isBuild && vitePlugins.push(...createCompression(viteEnv));
  return vitePlugins;
}
