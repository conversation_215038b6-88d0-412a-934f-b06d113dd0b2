<template>
  <div class="device-group-test">
    <h2>监控设备分组管理测试页面</h2>
    <p>这个页面用于测试监控设备分组管理功能</p>
    
    <!-- 功能说明 -->
    <el-card class="feature-card" header="功能说明">
      <el-row :gutter="20">
        <el-col :span="8">
          <h4>监控分组管理</h4>
          <ul>
            <li>创建新监控分组</li>
            <li>编辑分组信息</li>
            <li>删除空分组</li>
            <li>平级列表显示</li>
          </ul>
        </el-col>
        <el-col :span="8">
          <h4>摄像头管理</h4>
          <ul>
            <li>查看分组内摄像头</li>
            <li>添加摄像头到分组</li>
            <li>移动摄像头到其他分组</li>
            <li>从分组中移除摄像头</li>
          </ul>
        </el-col>
        <el-col :span="8">
          <h4>监控预览</h4>
          <ul>
            <li>多种分屏模式（1/4/6/9分屏）</li>
            <li>动态网格布局切换</li>
            <li>实时设备状态显示</li>
            <li>响应式设计适配</li>
          </ul>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 测试数据说明 -->
    <el-card class="data-card" header="测试数据说明">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="监控分组数量">5个（包含未分组）</el-descriptions-item>
        <el-descriptions-item label="摄像头总数">21个</el-descriptions-item>
        <el-descriptions-item label="在线摄像头">18个</el-descriptions-item>
        <el-descriptions-item label="离线摄像头">3个</el-descriptions-item>
        <el-descriptions-item label="分辨率类型">720P、1080P、4K</el-descriptions-item>
        <el-descriptions-item label="设备品牌">海康威视、大华、宇视</el-descriptions-item>
      </el-descriptions>
    </el-card>
    
    <!-- 操作指南 -->
    <el-card class="guide-card" header="操作指南">
      <el-steps :active="0" direction="vertical">
        <el-step title="选择监控分组" description="点击左侧分组列表中的任意分组查看其摄像头列表"></el-step>
        <el-step title="管理分组" description="使用'新增分组'按钮创建监控分组，或点击分组旁的编辑/删除按钮"></el-step>
        <el-step title="管理摄像头" description="使用'添加摄像头'按钮将摄像头加入分组，或拖拽摄像头进行移动"></el-step>
        <el-step title="切换分屏模式" description="使用右上角的分屏切换按钮选择1/4/6/9分屏显示模式"></el-step>
        <el-step title="监控预览" description="右侧区域显示当前分组的摄像头监控预览网格"></el-step>
      </el-steps>
    </el-card>
    
    <!-- 实际组件 -->
    <el-card class="component-card" header="监控设备分组管理组件">
      <MonitoringGroupPreview />
    </el-card>
  </div>
</template>

<script>
import MonitoringGroupPreview from '@/views/kaihua/MonitoringManagement/preview.vue'

export default {
  name: 'MonitoringGroupTest',
  components: {
    MonitoringGroupPreview
  }
}
</script>

<style lang="scss" scoped>
.device-group-test {
  padding: 20px;
  
  h2 {
    color: #333;
    margin-bottom: 10px;
  }
  
  p {
    color: #666;
    margin-bottom: 20px;
  }
  
  .feature-card,
  .data-card,
  .guide-card,
  .component-card {
    margin-bottom: 20px;
  }
  
  .component-card {
    .el-card__body {
      padding: 0;
      height: 600px;
    }
  }
  
  h4 {
    color: #409eff;
    margin-bottom: 10px;
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 5px;
      color: #666;
    }
  }
}
</style>
