import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/AlarmHostInfo/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/barrierGate/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (id) => {
  return request({
    url: '/AlarmHostInfo/remove',
    method: 'post',
    params: {
      id,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/AlarmHostInfo/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/AlarmHostInfo/update',
    method: 'post',
    data: row
  })
}

