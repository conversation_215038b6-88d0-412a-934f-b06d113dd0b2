/**
 * 设备运行数据相关API
 */
import request from '@/axios'

/**
 * 获取周设备运行数据
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期 (YYYY-MM-DD)
 * @param {string} params.endDate - 结束日期 (YYYY-MM-DD)
 * @returns {Promise} 返回周设备运行数据
 */
export const getWeekEquipmentData = (params) => {
  return request({
    url: '/api/equipment/week',
    method: 'get',
    params
  })
}

/**
 * 获取日设备运行数据
 * @param {Object} params - 查询参数
 * @param {string} params.date - 查询日期 (YYYY-MM-DD)
 * @returns {Promise} 返回日设备运行数据
 */
export const getDayEquipmentData = (params) => {
  return request({
    url: '/api/equipment/day',
    method: 'get',
    params
  })
}

/**
 * 获取实时设备运行数据
 * @returns {Promise} 返回实时设备运行数据
 */
export const getRealTimeEquipmentData = () => {
  return request({
    url: '/api/equipment/realtime',
    method: 'get'
  })
}

/**
 * 获取设备运行统计数据
 * @param {Object} params - 查询参数
 * @param {string} params.type - 统计类型 (week|month|year)
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @returns {Promise} 返回设备运行统计数据
 */
export const getEquipmentStatistics = (params) => {
  return request({
    url: '/api/equipment/statistics',
    method: 'get',
    params
  })
}

// API响应数据格式示例

/**
 * 周设备运行数据响应格式
 * {
 *   "code": 200,
 *   "message": "success",
 *   "data": [
 *     {
 *       "date": "2025-01-27",
 *       "dayOfWeek": "周一",
 *       "startup": 45,
 *       "shutdown": 38,
 *       "fault": 3,
 *       "alarm": 2
 *     },
 *     // ... 其他天数据
 *   ]
 * }
 */

/**
 * 日设备运行数据响应格式
 * {
 *   "code": 200,
 *   "message": "success",
 *   "data": [
 *     {
 *       "hour": "00:00",
 *       "startup": 2,
 *       "shutdown": 1,
 *       "fault": 0,
 *       "alarm": 0
 *     },
 *     // ... 其他小时数据
 *   ]
 * }
 */

/**
 * 实时设备运行数据响应格式
 * {
 *   "code": 200,
 *   "message": "success",
 *   "data": {
 *     "currentStartup": 25,
 *     "currentShutdown": 18,
 *     "currentFault": 2,
 *     "currentAlarm": 1,
 *     "todayStartupTotal": 156,
 *     "todayShutdownTotal": 142,
 *     "todayFaultTotal": 8,
 *     "todayAlarmTotal": 5,
 *     "monthStartupTotal": 3456,
 *     "monthShutdownTotal": 3201,
 *     "monthFaultTotal": 89,
 *     "monthAlarmTotal": 45,
 *     "lastUpdateTime": "2025-01-30T14:30:00Z"
 *   }
 * }
 */

/**
 * 设备运行统计数据响应格式
 * {
 *   "code": 200,
 *   "message": "success",
 *   "data": {
 *     "totalStartup": 12345,
 *     "totalShutdown": 11890,
 *     "totalFault": 234,
 *     "totalAlarm": 156,
 *     "averageStartup": 456.78,
 *     "averageShutdown": 423.45,
 *     "averageFault": 8.67,
 *     "averageAlarm": 5.78,
 *     "peakStartup": 89,
 *     "peakShutdown": 82,
 *     "peakFault": 12,
 *     "peakAlarm": 8,
 *     "peakStartupTime": "2025-01-25T18:30:00Z",
 *     "peakShutdownTime": "2025-01-25T18:45:00Z",
 *     "peakFaultTime": "2025-01-25T14:15:00Z",
 *     "peakAlarmTime": "2025-01-25T14:20:00Z",
 *     "trend": {
 *       "startup": "up", // up|down|stable
 *       "shutdown": "up",
 *       "fault": "down",
 *       "alarm": "stable",
 *       "startupChange": 5.2, // 百分比变化
 *       "shutdownChange": 3.8,
 *       "faultChange": -12.5,
 *       "alarmChange": 0.0
 *     }
 *   }
 * }
 */
