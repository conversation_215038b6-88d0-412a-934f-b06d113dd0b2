# 能耗统计图表组件 (EnergyConsumption)

## 功能概述

这是一个基于 ECharts 的能耗统计图表组件，支持周视图和日视图的切换，用于展示用电量和用水量数据。

## 功能特性

- ✅ **双视图模式**：支持周视图（柱状图）和日视图（折线图）切换
- ✅ **响应式设计**：自适应不同屏幕尺寸
- ✅ **交互式图表**：支持鼠标悬停显示详细数据
- ✅ **图例说明**：清晰区分用电量和用水量
- ✅ **数据单位**：用电量显示为 kWh，用水量显示为吨

## 视图说明

### 周视图（柱状图）
- **X轴**：显示一周的7天（周一到周日）
- **Y轴**：显示消耗量数值
- **图表类型**：并排柱状图
- **颜色**：蓝色（用电量）、绿色（用水量）

### 日视图（折线图）
- **X轴**：显示24小时（00:00-23:00）
- **Y轴**：显示消耗量数值
- **图表类型**：平滑折线图
- **颜色**：蓝色（用电量）、绿色（用水量）

## 使用方法

```vue
<template>
  <div class="chart-container">
    <Energyconsumption />
  </div>
</template>

<script setup>
import Energyconsumption from '@/components/wel/energyconsumption.vue'
</script>
```

## 数据格式

### 周数据格式
```javascript
const weekData = [
  { date: '周一', electricity: 120, water: 80 },
  { date: '周二', electricity: 132, water: 75 },
  // ... 其他天数据
]
```

### 日数据格式
```javascript
const dayData = [
  { hour: '00:00', electricity: 45, water: 20 },
  { hour: '01:00', electricity: 42, water: 18 },
  // ... 其他小时数据
]
```

## 数据字段说明

| 字段名 | 类型 | 说明 | 单位 |
|--------|------|------|------|
| date | String | 日期（周视图） | - |
| hour | String | 小时（日视图） | - |
| electricity | Number | 用电量 | kWh |
| water | Number | 用水量 | 吨 |

## 样式定制

组件支持通过 CSS 变量进行样式定制：

```scss
.energy-consumption-container {
  // 自定义容器高度
  height: 400px;
  
  .chart-container {
    // 自定义图表最小高度
    min-height: 300px;
  }
}
```

## 技术栈

- **Vue 3** - 组合式API
- **ECharts 6.0** - 图表库
- **Element Plus** - UI组件库
- **SCSS** - 样式预处理器

## 浏览器兼容性

- Chrome >= 60
- Firefox >= 60
- Safari >= 12
- Edge >= 79

## 更新日志

### v1.0.0 (2025-01-30)
- ✅ 初始版本发布
- ✅ 支持周视图柱状图
- ✅ 支持日视图折线图
- ✅ 响应式设计
- ✅ 交互式图表功能
