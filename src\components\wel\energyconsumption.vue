<template>
  <div class="energy-consumption-container">
    <!-- 时间切换按钮 -->
    <div class="view-toggle">
      <el-radio-group v-model="viewType" @change="handleViewChange">
        <el-radio-button label="week">周</el-radio-button>
        <el-radio-button label="day">日</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 图表容器 -->
    <div ref="chartRef" class="chart-container">
      <!-- 空状态提示 -->
      <div v-if="!chartInstance && !loading" class="empty-state">
        <el-empty description="暂无数据" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
// import { getWeekEnergyData, getDayEnergyData } from '@/api/energy'

// 响应式数据
const chartRef = ref(null)
const viewType = ref('week')
const loading = ref(false)
let chartInstance = null

// 模拟测试数据
const weekData = [
  { date: '周一', electricity: 120, water: 80 },
  { date: '周二', electricity: 132, water: 75 },
  { date: '周三', electricity: 101, water: 90 },
  { date: '周四', electricity: 134, water: 85 },
  { date: '周五', electricity: 90, water: 70 },
  { date: '周六', electricity: 230, water: 110 },
  { date: '周日', electricity: 210, water: 95 }
]

const dayData = [
  { hour: '00:00', electricity: 45, water: 20 },
  { hour: '01:00', electricity: 42, water: 18 },
  { hour: '02:00', electricity: 40, water: 15 },
  { hour: '03:00', electricity: 38, water: 12 },
  { hour: '04:00', electricity: 35, water: 10 },
  { hour: '05:00', electricity: 40, water: 15 },
  { hour: '06:00', electricity: 55, water: 25 },
  { hour: '07:00', electricity: 70, water: 35 },
  { hour: '08:00', electricity: 85, water: 45 },
  { hour: '09:00', electricity: 95, water: 50 },
  { hour: '10:00', electricity: 105, water: 55 },
  { hour: '11:00', electricity: 110, water: 60 },
  { hour: '12:00', electricity: 120, water: 65 },
  { hour: '13:00', electricity: 115, water: 62 },
  { hour: '14:00', electricity: 125, water: 68 },
  { hour: '15:00', electricity: 130, water: 70 },
  { hour: '16:00', electricity: 135, water: 72 },
  { hour: '17:00', electricity: 140, water: 75 },
  { hour: '18:00', electricity: 145, water: 78 },
  { hour: '19:00', electricity: 135, water: 70 },
  { hour: '20:00', electricity: 125, water: 65 },
  { hour: '21:00', electricity: 115, water: 60 },
  { hour: '22:00', electricity: 95, water: 50 },
  { hour: '23:00', electricity: 75, water: 40 }
]

// 获取周视图柱状图配置
const getWeekBarOption = () => {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        let result = params[0].name + '<br/>'
        params.forEach(param => {
          const unit = param.seriesName === '用电量' ? 'kWh' : '吨'
          result += `${param.marker}${param.seriesName}: ${param.value}${unit}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['用电量', '用水量'],
      top: 0,
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '0%',
      right: '2%',
      bottom: '3%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: weekData.map(item => item.date),
      axisLabel: {
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [
      {
        name: '用电量',
        type: 'bar',
        data: weekData.map(item => item.electricity),
        itemStyle: {
          color: '#409EFF'
        },
        barWidth: '30%'
      },
      {
        name: '用水量',
        type: 'bar',
        data: weekData.map(item => item.water),
        itemStyle: {
          color: '#67C23A'
        },
        barWidth: '30%'
      }
    ]
  }
}

// 获取日视图折线图配置
const getDayLineOption = () => {
  return {
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        let result = params[0].name + '<br/>'
        params.forEach(param => {
          const unit = param.seriesName === '用电量' ? 'kWh' : '吨'
          result += `${param.marker}${param.seriesName}: ${param.value}${unit}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['用电量', '用水量'],
      top: 0,
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '0%',
      right: '1%',
      bottom: '3%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dayData.map(item => item.hour),
      axisLabel: {
        color: '#666',
        rotate: 45
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [
      {
        name: '用电量',
        type: 'line',
        data: dayData.map(item => item.electricity),
        itemStyle: {
          color: '#409EFF'
        },
        lineStyle: {
          color: '#409EFF',
          width: 2
        },
        symbol: 'circle',
        symbolSize: 6,
        smooth: true
      },
      {
        name: '用水量',
        type: 'line',
        data: dayData.map(item => item.water),
        itemStyle: {
          color: '#67C23A'
        },
        lineStyle: {
          color: '#67C23A',
          width: 2
        },
        symbol: 'circle',
        symbolSize: 6,
        smooth: true
      }
    ]
  }
}

// 工具函数：获取当前周的开始日期
const getWeekStartDate = () => {
  const now = new Date()
  const dayOfWeek = now.getDay()
  const diff = now.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1) // 调整为周一开始
  const monday = new Date(now.setDate(diff))
  return monday.toISOString().split('T')[0]
}

// 工具函数：获取当前周的结束日期
const getWeekEndDate = () => {
  const startDate = new Date(getWeekStartDate())
  const endDate = new Date(startDate)
  endDate.setDate(startDate.getDate() + 6)
  return endDate.toISOString().split('T')[0]
}

// 工具函数：获取当前日期
const getCurrentDate = () => {
  return new Date().toISOString().split('T')[0]
}

// 获取数据的函数（目前使用模拟数据，可以替换为API调用）
const fetchData = async (type) => {
  loading.value = true
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    // 这里可以替换为真实的API调用
    // if (type === 'week') {
    //   const response = await getWeekEnergyData({
    //     startDate: getWeekStartDate(),
    //     endDate: getWeekEndDate()
    //   })
    //   return response.data
    // } else {
    //   const response = await getDayEnergyData({
    //     date: getCurrentDate()
    //   })
    //   return response.data
    // }

    // 返回模拟数据
    return type === 'week' ? weekData : dayData
  } catch (error) {
    console.error('获取能耗数据失败:', error)
    // 发生错误时返回模拟数据
    return type === 'week' ? weekData : dayData
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) return

  // 销毁已存在的图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 创建新的图表实例
  chartInstance = echarts.init(chartRef.value)

  // 显示加载动画
  chartInstance.showLoading({
    text: '数据加载中...',
    color: '#409EFF',
    textColor: '#666',
    maskColor: 'rgba(255, 255, 255, 0.8)',
    zlevel: 0
  })

  // 获取数据
  await fetchData(viewType.value)

  // 隐藏加载动画
  chartInstance.hideLoading()

  // 根据视图类型设置配置
  const option = viewType.value === 'week' ? getWeekBarOption() : getDayLineOption()
  chartInstance.setOption(option)

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 处理视图切换
const handleViewChange = () => {
  nextTick(() => {
    initChart()
  })
}

// 组件挂载时初始化图表
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
.energy-consumption-container {
  width: 100%;
  height: calc(100% - 35px);
  display: flex;
  flex-direction: column;

  .view-toggle {
    display: flex;
    justify-content: center;
    position: absolute;
    right: 16px;
    top: 16px;
    
  }

  .chart-container {
    flex: 1;
    max-height: 120px;
    width: 100%;
    position: relative;
    margin-top: 20px;
    .empty-state {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .energy-consumption-container {
    .view-toggle {
      margin-bottom: 12px;

      :deep(.el-radio-group) {
        .el-radio-button__inner {
          padding: 6px 12px;
          font-size: 12px;
        }
      }
    }

    .chart-container {
      min-height: 250px;
    }
  }
}
</style>