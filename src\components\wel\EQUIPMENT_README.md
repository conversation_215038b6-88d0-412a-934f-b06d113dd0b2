# 楼宇控制设备运行趋势图表组件 (EquipmentOperation)

## 功能概述

这是一个基于 ECharts 的楼宇控制设备运行趋势图表组件，支持周视图和日视图的切换，用于展示设备的启动数、停止数、故障数、报警数等运行状态数据。

## 功能特性

- ✅ **双视图模式**：支持周视图（柱状图）和日视图（折线图）切换
- ✅ **四个数据系列**：启动数、停止数、故障数、报警数
- ✅ **响应式设计**：自适应不同屏幕尺寸
- ✅ **交互式图表**：支持鼠标悬停显示详细数据
- ✅ **图例说明**：清晰区分四种设备运行状态
- ✅ **数据单位**：所有数据显示为"次数"

## 视图说明

### 周视图（柱状图）
- **X轴**：显示一周的7天（周一到周日）
- **Y轴**：显示运行次数
- **图表类型**：并排柱状图
- **颜色方案**：
  - 蓝色（#409EFF）：启动数
  - 绿色（#67C23A）：停止数
  - 橙色（#E6A23C）：故障数
  - 红色（#F56C6C）：报警数

### 日视图（折线图）
- **X轴**：显示24小时（00:00-23:00）
- **Y轴**：显示运行次数
- **图表类型**：平滑折线图
- **颜色方案**：与周视图保持一致

## 使用方法

```vue
<template>
  <div class="chart-container">
    <Equipmentoperation />
  </div>
</template>

<script setup>
import Equipmentoperation from '@/components/wel/equipmentoperation.vue'
</script>
```

## 数据格式

### 周数据格式
```javascript
const weekData = [
  { date: '周一', startup: 45, shutdown: 38, fault: 3, alarm: 2 },
  { date: '周二', startup: 52, shutdown: 41, fault: 2, alarm: 1 },
  // ... 其他天数据
]
```

### 日数据格式
```javascript
const dayData = [
  { hour: '00:00', startup: 2, shutdown: 1, fault: 0, alarm: 0 },
  { hour: '01:00', startup: 1, shutdown: 2, fault: 0, alarm: 0 },
  // ... 其他小时数据
]
```

## 数据字段说明

| 字段名 | 类型 | 说明 | 单位 | 颜色 |
|--------|------|------|------|------|
| date | String | 日期（周视图） | - | - |
| hour | String | 小时（日视图） | - | - |
| startup | Number | 启动次数 | 次 | 蓝色 |
| shutdown | Number | 停止次数 | 次 | 绿色 |
| fault | Number | 故障次数 | 次 | 橙色 |
| alarm | Number | 报警次数 | 次 | 红色 |

## 样式定制

组件支持通过 CSS 变量进行样式定制：

```scss
.equipment-operation-container {
  // 自定义容器高度
  height: 184px;
  
  .chart-container {
    // 自定义图表最大高度
    max-height: 120px;
  }
}
```

## 技术栈

- **Vue 3** - 组合式API
- **ECharts 6.0** - 图表库
- **Element Plus** - UI组件库
- **SCSS** - 样式预处理器

## API集成

可以通过以下API接口获取真实数据：

```javascript
import { getWeekEquipmentData, getDayEquipmentData } from '@/api/equipment'

// 获取周数据
const weekData = await getWeekEquipmentData({
  startDate: '2025-01-27',
  endDate: '2025-02-02'
})

// 获取日数据
const dayData = await getDayEquipmentData({
  date: '2025-01-30'
})
```

## 交互功能

### 鼠标悬停提示
- 显示具体时间点
- 显示四个数据系列的数值
- 显示单位"次"

### 视图切换
- 周视图：显示一周7天的设备运行汇总
- 日视图：显示24小时的设备运行趋势

## 浏览器兼容性

- Chrome >= 60
- Firefox >= 60
- Safari >= 12
- Edge >= 79

## 更新日志

### v1.0.0 (2025-01-30)
- ✅ 初始版本发布
- ✅ 支持周视图柱状图
- ✅ 支持日视图折线图
- ✅ 四个数据系列展示
- ✅ 响应式设计
- ✅ 交互式图表功能

## 扩展功能

组件设计时考虑了扩展性，可以轻松添加：
- 更多时间维度（月视图、年视图）
- 更多设备状态类型
- 设备分类筛选
- 数据导出功能
- 实时数据更新
- 设备状态对比功能

## 总结

楼宇控制设备运行趋势图表组件提供了完整的设备运行状态监控功能，具备：
- ✅ 完整的功能实现
- ✅ 良好的用户体验
- ✅ 响应式设计
- ✅ 可扩展的架构
- ✅ 完善的文档

组件可以立即投入使用，并且具备良好的可维护性和扩展性。
