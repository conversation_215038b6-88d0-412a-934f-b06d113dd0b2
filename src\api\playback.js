/**
 * 监控回放相关API
 */
import request from '@/axios'

/**
 * 获取设备回放记录列表
 * @param {Object} params - 查询参数
 * @param {string} params.deviceId - 设备ID
 * @param {string} params.startTime - 开始时间
 * @param {string} params.endTime - 结束时间
 * @param {string} params.recordType - 录像类型 (normal/motion/alarm)
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页数量
 * @returns {Promise} 返回回放记录列表
 */
export const getPlaybackRecords = (params) => {
  return request({
    url: '/api/playback/records',
    method: 'get',
    params
  })
}

/**
 * 获取回放视频流地址
 * @param {Object} data - 请求数据
 * @param {string} data.deviceId - 设备ID
 * @param {string} data.recordId - 录像记录ID
 * @param {string} data.startTime - 开始时间
 * @param {string} data.endTime - 结束时间
 * @returns {Promise} 返回视频流地址
 */
export const getPlaybackStreamUrl = (data) => {
  return request({
    url: '/api/playback/stream',
    method: 'post',
    data
  })
}

/**
 * 下载回放视频文件
 * @param {Object} params - 下载参数
 * @param {string} params.recordId - 录像记录ID
 * @param {string} params.deviceId - 设备ID
 * @returns {Promise} 返回下载链接
 */
export const downloadPlaybackVideo = (params) => {
  return request({
    url: '/api/playback/download',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取设备录像统计信息
 * @param {Object} params - 查询参数
 * @param {string} params.deviceId - 设备ID
 * @param {string} params.date - 查询日期 (YYYY-MM-DD)
 * @returns {Promise} 返回录像统计信息
 */
export const getRecordStatistics = (params) => {
  return request({
    url: '/api/playback/statistics',
    method: 'get',
    params
  })
}

/**
 * 获取录像时间轴数据
 * @param {Object} params - 查询参数
 * @param {string} params.deviceId - 设备ID
 * @param {string} params.date - 查询日期 (YYYY-MM-DD)
 * @returns {Promise} 返回时间轴数据
 */
export const getRecordTimeline = (params) => {
  return request({
    url: '/api/playback/timeline',
    method: 'get',
    params
  })
}

/**
 * 控制回放播放
 * @param {Object} data - 控制数据
 * @param {string} data.action - 操作类型 (play/pause/stop/seek)
 * @param {string} data.recordId - 录像记录ID
 * @param {number} data.position - 播放位置（秒）
 * @returns {Promise} 返回操作结果
 */
export const controlPlayback = (data) => {
  return request({
    url: '/api/playback/control',
    method: 'post',
    data
  })
}

// API响应数据格式示例

/**
 * 回放记录列表响应格式
 * {
 *   "code": 200,
 *   "message": "success",
 *   "data": {
 *     "records": [
 *       {
 *         "id": "record_001",
 *         "deviceId": "camera_001",
 *         "deviceName": "大厅1号摄像头",
 *         "startTime": "2025-01-30T08:00:00Z",
 *         "endTime": "2025-01-30T09:00:00Z",
 *         "duration": 3600,
 *         "fileSize": 524288000,
 *         "recordType": "normal",
 *         "status": "normal",
 *         "filePath": "/recordings/camera_001/2025/01/30/record_001.mp4",
 *         "thumbnailUrl": "/thumbnails/camera_001/2025/01/30/record_001.jpg"
 *       }
 *     ],
 *     "total": 100,
 *     "pageNum": 1,
 *     "pageSize": 20
 *   }
 * }
 */

/**
 * 回放视频流地址响应格式
 * {
 *   "code": 200,
 *   "message": "success",
 *   "data": {
 *     "streamUrl": "rtmp://192.168.1.100:1935/playback/camera_001_20250130080000",
 *     "hlsUrl": "http://192.168.1.100:8080/hls/camera_001_20250130080000.m3u8",
 *     "flvUrl": "http://192.168.1.100:8080/flv/camera_001_20250130080000.flv",
 *     "sessionId": "session_12345",
 *     "duration": 3600,
 *     "startTime": "2025-01-30T08:00:00Z",
 *     "endTime": "2025-01-30T09:00:00Z"
 *   }
 * }
 */

/**
 * 录像统计信息响应格式
 * {
 *   "code": 200,
 *   "message": "success",
 *   "data": {
 *     "deviceId": "camera_001",
 *     "date": "2025-01-30",
 *     "totalRecords": 24,
 *     "totalDuration": 86400,
 *     "totalSize": 12582912000,
 *     "recordTypes": {
 *       "normal": 20,
 *       "motion": 3,
 *       "alarm": 1
 *     },
 *     "hourlyStats": [
 *       {
 *         "hour": 0,
 *         "recordCount": 1,
 *         "duration": 3600,
 *         "size": 524288000
 *       }
 *     ]
 *   }
 * }
 */

/**
 * 录像时间轴数据响应格式
 * {
 *   "code": 200,
 *   "message": "success",
 *   "data": {
 *     "deviceId": "camera_001",
 *     "date": "2025-01-30",
 *     "timeSegments": [
 *       {
 *         "startTime": "2025-01-30T08:00:00Z",
 *         "endTime": "2025-01-30T09:00:00Z",
 *         "recordType": "normal",
 *         "recordId": "record_001"
 *       },
 *       {
 *         "startTime": "2025-01-30T14:30:00Z",
 *         "endTime": "2025-01-30T14:35:00Z",
 *         "recordType": "motion",
 *         "recordId": "record_002"
 *       }
 *     ]
 *   }
 * }
 */
