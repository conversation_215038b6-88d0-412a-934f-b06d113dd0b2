/**
 * 能耗数据相关API
 */
import request from '@/axios'

/**
 * 获取周能耗数据
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期 (YYYY-MM-DD)
 * @param {string} params.endDate - 结束日期 (YYYY-MM-DD)
 * @returns {Promise} 返回周能耗数据
 */
export const getWeekEnergyData = (params) => {
  return request({
    url: '/api/energy/week',
    method: 'get',
    params
  })
}

/**
 * 获取日能耗数据
 * @param {Object} params - 查询参数
 * @param {string} params.date - 查询日期 (YYYY-MM-DD)
 * @returns {Promise} 返回日能耗数据
 */
export const getDayEnergyData = (params) => {
  return request({
    url: '/api/energy/day',
    method: 'get',
    params
  })
}

/**
 * 获取实时能耗数据
 * @returns {Promise} 返回实时能耗数据
 */
export const getRealTimeEnergyData = () => {
  return request({
    url: '/api/energy/realtime',
    method: 'get'
  })
}

/**
 * 获取能耗统计数据
 * @param {Object} params - 查询参数
 * @param {string} params.type - 统计类型 (week|month|year)
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @returns {Promise} 返回能耗统计数据
 */
export const getEnergyStatistics = (params) => {
  return request({
    url: '/api/energy/statistics',
    method: 'get',
    params
  })
}

// API响应数据格式示例

/**
 * 周能耗数据响应格式
 * {
 *   "code": 200,
 *   "message": "success",
 *   "data": [
 *     {
 *       "date": "2025-01-27",
 *       "dayOfWeek": "周一",
 *       "electricity": 120.5,
 *       "water": 80.2,
 *       "electricityCost": 96.4,
 *       "waterCost": 240.6
 *     },
 *     // ... 其他天数据
 *   ]
 * }
 */

/**
 * 日能耗数据响应格式
 * {
 *   "code": 200,
 *   "message": "success",
 *   "data": [
 *     {
 *       "hour": "00:00",
 *       "electricity": 45.2,
 *       "water": 20.1,
 *       "temperature": 22.5,
 *       "humidity": 65.2
 *     },
 *     // ... 其他小时数据
 *   ]
 * }
 */

/**
 * 实时能耗数据响应格式
 * {
 *   "code": 200,
 *   "message": "success",
 *   "data": {
 *     "currentElectricity": 125.6,
 *     "currentWater": 85.3,
 *     "todayElectricityTotal": 2456.8,
 *     "todayWaterTotal": 1234.5,
 *     "monthElectricityTotal": 45678.9,
 *     "monthWaterTotal": 23456.7,
 *     "lastUpdateTime": "2025-01-30T14:30:00Z"
 *   }
 * }
 */

/**
 * 能耗统计数据响应格式
 * {
 *   "code": 200,
 *   "message": "success",
 *   "data": {
 *     "totalElectricity": 12345.67,
 *     "totalWater": 6789.12,
 *     "averageElectricity": 456.78,
 *     "averageWater": 234.56,
 *     "peakElectricity": 890.12,
 *     "peakWater": 345.67,
 *     "peakElectricityTime": "2025-01-25T18:30:00Z",
 *     "peakWaterTime": "2025-01-25T12:15:00Z",
 *     "trend": {
 *       "electricity": "up", // up|down|stable
 *       "water": "down",
 *       "electricityChange": 5.2, // 百分比变化
 *       "waterChange": -2.8
 *     }
 *   }
 * }
 */
