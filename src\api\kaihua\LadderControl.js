import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/buildingEquipment/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/barrierGate/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (id) => {
  return request({
    url: '/buildingEquipment/remove',
    method: 'post',
    params: {
      id,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/buildingEquipment/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/buildingEquipment/update',
    method: 'post',
    data: row
  })
}

