<template>
  <div class="preview">
    <el-row :gutter="10">
      <el-col :span="6">
        <!-- 监控分组浏览 -->
        <div class="group-box">
          <div class="group-header">
            <h3 class="group-title">监控分组</h3>
          </div>

          <!-- 监控分组列表 -->
          <div class="group-list-container">
            <el-scrollbar height="300px">
              <div class="group-list">
                <div
                  v-for="group in groupListData"
                  :key="group.id"
                  class="group-item"
                  :class="{ 'active': selectedGroup?.id === group.id }"
                  @click="handleGroupClick(group)"
                >
                  <div class="group-content">
                    <div class="group-info">
                      <el-icon class="group-icon">
                        <Monitor />
                      </el-icon>
                      <div class="group-details">
                        <div class="group-name">{{ group.name }}</div>
                        <div class="group-desc">{{ group.description }}</div>
                      </div>
                      <div class="device-count">
                        <el-tag size="small" type="info">{{ group.deviceCount || 0 }}台</el-tag>
                      </div>
                    </div>

                  </div>
                </div>
              </div>
            </el-scrollbar>
          </div>

          <!-- 当前分组的监控设备列表 -->
          <div class="group-devices" v-if="selectedGroup">
            <div class="devices-header">
              <h4>{{ selectedGroup.name }} - 监控设备</h4>
              <span class="device-count-info">共{{ currentGroupDevices.length }}个设备</span>
            </div>

            <div class="devices-list">
              <el-scrollbar height="300px">
                <div
                  v-for="device in currentGroupDevices"
                  :key="device.id"
                  class="device-item"
                  :class="{ 'selected': selectedDevice?.id === device.id }"
                  @click="selectDeviceForPlayback(device)"
                >
                  <div class="device-info">
                    <el-icon class="device-icon">
                      <Monitor />
                    </el-icon>
                    <div class="device-details">
                      <div class="device-name">{{ device.name }}</div>
                      <div class="device-meta">
                        <span class="device-no">{{ device.deviceNo }}</span>
                        <span class="device-location">{{ device.location }}</span>
                        <span class="device-resolution">{{ device.resolution }}</span>
                      </div>
                    </div>
                    <div class="device-status">
                      <el-tag
                        :type="device.status === 'online' ? 'success' : 'danger'"
                        size="small"
                      >
                        {{ device.status === 'online' ? '在线' : '离线' }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="device-select-indicator" v-if="selectedDevice?.id === device.id">
                    <el-icon color="#409eff"><Check /></el-icon>
                  </div>
                </div>

                <el-empty
                  v-if="currentGroupDevices.length === 0"
                  description="暂无设备"
                  :image-size="80"
                />
              </el-scrollbar>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="18">
        <!-- 监控回放 -->
        <div class="playback-box">
          <div class="playback-header">
            <div class="playback-header-left">
              <h3>监控回放</h3>
              <div class="playback-info" v-if="selectedDevice">
                当前设备：{{ selectedDevice.name }} ({{ selectedDevice.deviceNo }})
              </div>
              <div class="playback-info" v-else>
                请选择左侧设备查看回放记录
              </div>
            </div>
          </div>

          <!-- 时间筛选控件 -->
          <div class="time-filter-section" v-if="selectedDevice">
            <div class="time-filter-controls">
              <div class="quick-time-buttons">
                <span class="filter-label">快捷选择：</span>
                <el-button-group>
                  <el-button
                    size="small"
                    :type="quickTimeType === 'today' ? 'primary' : ''"
                    @click="selectQuickTime('today')"
                  >
                    今天
                  </el-button>
                  <el-button
                    size="small"
                    :type="quickTimeType === 'yesterday' ? 'primary' : ''"
                    @click="selectQuickTime('yesterday')"
                  >
                    昨天
                  </el-button>
                  <el-button
                    size="small"
                    :type="quickTimeType === 'week' ? 'primary' : ''"
                    @click="selectQuickTime('week')"
                  >
                    最近7天
                  </el-button>
                  <el-button
                    size="small"
                    :type="quickTimeType === 'month' ? 'primary' : ''"
                    @click="selectQuickTime('month')"
                  >
                    最近30天
                  </el-button>
                </el-button-group>
              </div>

              <div class="custom-time-range">
                <span class="filter-label">自定义时间：</span>
                <el-date-picker
                  v-model="startTime"
                  type="datetime"
                  placeholder="开始时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  size="small"
                  style="width: 180px; margin-right: 10px;"
                  @change="handleTimeChange"
                />
                <span style="margin: 0 8px;">至</span>
                <el-date-picker
                  v-model="endTime"
                  type="datetime"
                  placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  size="small"
                  style="width: 180px; margin-right: 10px;"
                  @change="handleTimeChange"
                />
                <el-button
                  type="primary"
                  size="small"
                  icon="Search"
                  @click="searchPlaybackRecords"
                  :loading="playbackLoading"
                >
                  查询
                </el-button>
              </div>
            </div>
          </div>

          <!-- 回放列表 -->
          <div class="playback-content" v-if="selectedDevice">
            <el-table
              :data="playbackRecords"
              v-loading="playbackLoading"
              height="500"
              stripe
              border
            >
              <el-table-column prop="startTime" label="录像开始时间" width="180">
                <template #default="scope">
                  {{ formatDateTime(scope.row.startTime) }}
                </template>
              </el-table-column>
              <el-table-column prop="endTime" label="录像结束时间" width="180">
                <template #default="scope">
                  {{ formatDateTime(scope.row.endTime) }}
                </template>
              </el-table-column>
              <el-table-column prop="duration" label="录像时长" width="120">
                <template #default="scope">
                  {{ formatDuration(scope.row.duration) }}
                </template>
              </el-table-column>
              <el-table-column prop="fileSize" label="文件大小" width="120">
                <template #default="scope">
                  {{ formatFileSize(scope.row.fileSize) }}
                </template>
              </el-table-column>
              <el-table-column prop="recordType" label="录像类型" width="100">
                <template #default="scope">
                  <el-tag
                    :type="getRecordTypeColor(scope.row.recordType)"
                    size="small"
                  >
                    {{ getRecordTypeText(scope.row.recordType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template #default="scope">
                  <el-tag
                    :type="scope.row.status === 'normal' ? 'success' : 'warning'"
                    size="small"
                  >
                    {{ scope.row.status === 'normal' ? '正常' : '异常' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" fixed="right">
                <template #default="scope">
                  <el-button
                    type="primary"
                    size="small"
                    icon="VideoPlay"
                    @click="playRecord(scope.row)"
                  >
                    播放
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <el-empty
              v-if="!playbackLoading && playbackRecords.length === 0 && selectedDevice"
              description="暂无回放记录"
              :image-size="120"
            />
          </div>

          <!-- 未选择设备时的提示 -->
          <div class="no-device-selected" v-else>
            <el-empty
              description="请在左侧选择监控设备查看回放记录"
              :image-size="150"
            />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  getMonitoringGroupList,
  getAllMonitoringDevices
} from '@/api/deviceGroup'
import { ElMessage } from 'element-plus'
import {
  Monitor,
  Search,
  Check,
  VideoPlay
} from '@element-plus/icons-vue'

export default {
  name: 'MonitoringPlayback',
  components: {
    Monitor,
    Search,
    Check,
    VideoPlay
  },
  data() {
    return {
      // 监控分组列表数据（平级结构）
      groupListData: [],
      // 当前选中的分组
      selectedGroup: null,
      // 当前分组的监控设备列表
      currentGroupDevices: [],
      // 所有可用监控设备列表
      allDevices: [],
      // 当前选中的设备
      selectedDevice: null,

      // 回放相关
      playbackRecords: [],
      playbackLoading: false,

      // 时间筛选相关
      startTime: '',
      endTime: '',
      quickTimeType: 'today', // today, yesterday, week, month, custom

      // 加载状态
      loading: false
    }
  },
  computed: {
    // 过滤后的回放记录（根据时间筛选）
    filteredPlaybackRecords() {
      if (!this.startTime || !this.endTime) {
        return this.playbackRecords
      }
      return this.playbackRecords.filter(record => {
        const recordTime = new Date(record.startTime)
        const start = new Date(this.startTime)
        const end = new Date(this.endTime)
        return recordTime >= start && recordTime <= end
      })
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    // 初始化数据
    async initData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadGroupList(),
          this.loadAllDevices()
        ])
        // 默认选中未分组
        const ungroupedNode = this.groupListData.find(group => group.id === 'ungrouped')
        if (ungroupedNode) {
          this.handleGroupClick(ungroupedNode)
        }
        // 初始化时间为今天
        this.selectQuickTime('today')
      } catch (error) {
        ElMessage.error('数据加载失败：' + error.message)
      } finally {
        this.loading = false
      }
    },

    // 加载监控分组列表数据
    async loadGroupList() {
      try {
        const response = await getMonitoringGroupList()
        if (response.data.code === 200) {
          this.groupListData = response.data.data
        } else {
          // 如果API未实现，使用模拟数据
          this.groupListData = this.getMockGroupData()
        }
      } catch (error) {
        console.warn('API未实现，使用模拟数据')
        this.groupListData = this.getMockGroupData()
      }
    },

    // 加载所有监控设备数据
    async loadAllDevices() {
      try {
        const response = await getAllMonitoringDevices()
        if (response.data.code === 200) {
          this.allDevices = response.data.data
        } else {
          // 如果API未实现，使用模拟数据
          this.allDevices = this.getMockDeviceData()
        }
      } catch (error) {
        console.warn('API未实现，使用模拟数据')
        this.allDevices = this.getMockDeviceData()
      }
    },

    // 获取模拟监控分组数据（平级结构）
    getMockGroupData() {
      return [
        {
          id: 'ungrouped',
          name: '未分组',
          description: '未分配到具体分组的监控设备',
          deviceCount: 3,
          isDefault: true,
          deletable: false
        },
        {
          id: 'group_001',
          name: '大厅监控',
          description: '大厅区域的监控摄像头',
          deviceCount: 4,
          isDefault: false,
          deletable: true
        },
        {
          id: 'group_002',
          name: '办公区监控',
          description: '办公区域的监控摄像头',
          deviceCount: 6,
          isDefault: false,
          deletable: true
        },
        {
          id: 'group_003',
          name: '室外监控',
          description: '室外区域的监控摄像头',
          deviceCount: 5,
          isDefault: false,
          deletable: true
        },
        {
          id: 'group_004',
          name: '停车场监控',
          description: '停车场区域的监控摄像头',
          deviceCount: 3,
          isDefault: false,
          deletable: true
        }
      ]
    },

    // 获取模拟监控设备数据（仅摄像头）
    getMockDeviceData() {
      return [
        // 未分组监控设备
        { id: 'camera_001', name: '临时摄像头1', deviceNo: 'TEMP001', type: 'camera', status: 'online', location: '临时位置1', resolution: '1080P', brand: '海康威视', groupId: 'ungrouped' },
        { id: 'camera_002', name: '临时摄像头2', deviceNo: 'TEMP002', type: 'camera', status: 'offline', location: '临时位置2', resolution: '720P', brand: '大华', groupId: 'ungrouped' },
        { id: 'camera_003', name: '备用摄像头', deviceNo: 'BACKUP001', type: 'camera', status: 'online', location: '备用位置', resolution: '4K', brand: '宇视', groupId: 'ungrouped' },

        // 大厅监控设备
        { id: 'camera_004', name: '大厅入口摄像头', deviceNo: 'HALL001', type: 'camera', status: 'online', location: '大厅入口', resolution: '1080P', brand: '海康威视', groupId: 'group_001' },
        { id: 'camera_005', name: '大厅中央摄像头', deviceNo: 'HALL002', type: 'camera', status: 'online', location: '大厅中央', resolution: '4K', brand: '海康威视', groupId: 'group_001' },
        { id: 'camera_006', name: '大厅出口摄像头', deviceNo: 'HALL003', type: 'camera', status: 'online', location: '大厅出口', resolution: '1080P', brand: '大华', groupId: 'group_001' },
        { id: 'camera_007', name: '大厅服务台摄像头', deviceNo: 'HALL004', type: 'camera', status: 'offline', location: '服务台', resolution: '1080P', brand: '宇视', groupId: 'group_001' },

        // 办公区监控设备
        { id: 'camera_008', name: '办公区走廊1', deviceNo: 'OFFICE001', type: 'camera', status: 'online', location: '办公区走廊1', resolution: '1080P', brand: '海康威视', groupId: 'group_002' },
        { id: 'camera_009', name: '办公区走廊2', deviceNo: 'OFFICE002', type: 'camera', status: 'online', location: '办公区走廊2', resolution: '1080P', brand: '海康威视', groupId: 'group_002' },
        { id: 'camera_010', name: '会议室A摄像头', deviceNo: 'MEET001', type: 'camera', status: 'online', location: '会议室A', resolution: '4K', brand: '大华', groupId: 'group_002' },
        { id: 'camera_011', name: '会议室B摄像头', deviceNo: 'MEET002', type: 'camera', status: 'offline', location: '会议室B', resolution: '1080P', brand: '宇视', groupId: 'group_002' },
        { id: 'camera_012', name: '办公区电梯口', deviceNo: 'OFFICE003', type: 'camera', status: 'online', location: '电梯口', resolution: '1080P', brand: '海康威视', groupId: 'group_002' },
        { id: 'camera_013', name: '办公区茶水间', deviceNo: 'OFFICE004', type: 'camera', status: 'online', location: '茶水间', resolution: '720P', brand: '大华', groupId: 'group_002' },

        // 室外监控设备
        { id: 'camera_014', name: '主入口摄像头', deviceNo: 'OUTDOOR001', type: 'camera', status: 'online', location: '主入口', resolution: '4K', brand: '海康威视', groupId: 'group_003' },
        { id: 'camera_015', name: '侧门摄像头', deviceNo: 'OUTDOOR002', type: 'camera', status: 'online', location: '侧门', resolution: '1080P', brand: '大华', groupId: 'group_003' },
        { id: 'camera_016', name: '花园摄像头', deviceNo: 'OUTDOOR003', type: 'camera', status: 'offline', location: '花园', resolution: '1080P', brand: '宇视', groupId: 'group_003' },
        { id: 'camera_017', name: '围墙摄像头1', deviceNo: 'OUTDOOR004', type: 'camera', status: 'online', location: '围墙东侧', resolution: '1080P', brand: '海康威视', groupId: 'group_003' },
        { id: 'camera_018', name: '围墙摄像头2', deviceNo: 'OUTDOOR005', type: 'camera', status: 'online', location: '围墙西侧', resolution: '1080P', brand: '大华', groupId: 'group_003' },

        // 停车场监控设备
        { id: 'camera_019', name: '停车场入口', deviceNo: 'PARK001', type: 'camera', status: 'online', location: '停车场入口', resolution: '4K', brand: '海康威视', groupId: 'group_004' },
        { id: 'camera_020', name: '停车场A区', deviceNo: 'PARK002', type: 'camera', status: 'online', location: '停车场A区', resolution: '1080P', brand: '大华', groupId: 'group_004' },
        { id: 'camera_021', name: '停车场B区', deviceNo: 'PARK003', type: 'camera', status: 'offline', location: '停车场B区', resolution: '1080P', brand: '宇视', groupId: 'group_004' }
      ]
    },

    // 分组点击事件
    async handleGroupClick(group) {
      this.selectedGroup = group
      await this.loadGroupDevices(group.id)
    },

    // 加载分组内的设备
    async loadGroupDevices(groupId) {
      try {
        // 从模拟数据中筛选设备
        this.currentGroupDevices = this.allDevices.filter(device => device.groupId === groupId)

        // 更新分组的设备数量
        this.updateGroupDeviceCount(groupId, this.currentGroupDevices.length)
      } catch (error) {
        ElMessage.error('加载设备列表失败：' + error.message)
      }
    },

    // 更新分组设备数量
    updateGroupDeviceCount(groupId, count) {
      const group = this.groupListData.find(g => g.id === groupId)
      if (group) {
        group.deviceCount = count
      }
    },

    // 选择设备进行回放查询
    async selectDeviceForPlayback(device) {
      this.selectedDevice = device
      console.log('选择设备进行回放查询:', device)

      // 自动查询回放记录
      await this.searchPlaybackRecords()
    },

    // 快捷时间选择
    selectQuickTime(type) {
      this.quickTimeType = type
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

      switch (type) {
        case 'today':
          this.startTime = this.formatDateTime(today)
          this.endTime = this.formatDateTime(new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1))
          break
        case 'yesterday':
          const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
          this.startTime = this.formatDateTime(yesterday)
          this.endTime = this.formatDateTime(new Date(yesterday.getTime() + 24 * 60 * 60 * 1000 - 1))
          break
        case 'week':
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
          this.startTime = this.formatDateTime(weekAgo)
          this.endTime = this.formatDateTime(now)
          break
        case 'month':
          const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
          this.startTime = this.formatDateTime(monthAgo)
          this.endTime = this.formatDateTime(now)
          break
      }

      // 如果已选择设备，自动查询
      if (this.selectedDevice) {
        this.searchPlaybackRecords()
      }
    },

    // 时间变化处理
    handleTimeChange() {
      this.quickTimeType = 'custom'
    },

    // 查询回放记录
    async searchPlaybackRecords() {
      if (!this.selectedDevice) {
        ElMessage.warning('请先选择设备')
        return
      }

      if (!this.startTime || !this.endTime) {
        ElMessage.warning('请选择查询时间范围')
        return
      }

      this.playbackLoading = true
      try {
        // 这里调用回放查询API
        // const response = await getPlaybackRecords({
        //   deviceId: this.selectedDevice.id,
        //   startTime: this.startTime,
        //   endTime: this.endTime
        // })

        // 使用模拟数据
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟网络延迟
        this.playbackRecords = this.getMockPlaybackData()

        ElMessage.success(`查询到 ${this.playbackRecords.length} 条回放记录`)
      } catch (error) {
        ElMessage.error('查询回放记录失败：' + error.message)
        this.playbackRecords = []
      } finally {
        this.playbackLoading = false
      }
    },

    // 播放回放记录
    playRecord(record) {
      console.log('播放回放记录:', record)
      ElMessage.info(`开始播放 ${this.formatDateTime(record.startTime)} 的录像`)
      // 这里可以集成视频播放器
    },

    // 获取模拟回放数据
    getMockPlaybackData() {
      const records = []
      const startDate = new Date(this.startTime)
      const endDate = new Date(this.endTime)

      // 生成模拟回放记录
      for (let i = 0; i < 10; i++) {
        const recordStart = new Date(startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime()))
        const duration = Math.floor(Math.random() * 3600) + 300 // 5分钟到1小时
        const recordEnd = new Date(recordStart.getTime() + duration * 1000)

        records.push({
          id: `record_${i + 1}`,
          startTime: recordStart.toISOString(),
          endTime: recordEnd.toISOString(),
          duration: duration,
          fileSize: Math.floor(Math.random() * 1000000000) + 100000000, // 100MB到1GB
          recordType: ['normal', 'motion', 'alarm'][Math.floor(Math.random() * 3)],
          status: Math.random() > 0.1 ? 'normal' : 'error',
          filePath: `/recordings/${this.selectedDevice.deviceNo}/${recordStart.getFullYear()}/${recordStart.getMonth() + 1}/${recordStart.getDate()}/record_${i + 1}.mp4`
        })
      }

      return records.sort((a, b) => new Date(b.startTime) - new Date(a.startTime))
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      const date = new Date(dateTime)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 格式化时长
    formatDuration(seconds) {
      if (!seconds) return '0秒'
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60

      if (hours > 0) {
        return `${hours}时${minutes}分${secs}秒`
      } else if (minutes > 0) {
        return `${minutes}分${secs}秒`
      } else {
        return `${secs}秒`
      }
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return '0B'
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    },

    // 获取录像类型颜色
    getRecordTypeColor(type) {
      const colors = {
        normal: '',
        motion: 'warning',
        alarm: 'danger'
      }
      return colors[type] || ''
    },

    // 获取录像类型文本
    getRecordTypeText(type) {
      const texts = {
        normal: '正常录像',
        motion: '移动侦测',
        alarm: '报警录像'
      }
      return texts[type] || '未知'
    },


  }
}
</script>

<style lang="scss" scoped>
.preview {
  padding: 10px;
  height: calc(100vh - 120px);
}

// 监控分组管理区域
.group-box {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;

  .group-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;

    .group-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: white;
    }

    .el-button {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.3);
      color: white;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
      }
    }
  }

  .group-list-container {
    flex: 1;
    padding: 8px;
    overflow: auto;

    .group-list {
      .group-item {
        margin-bottom: 8px;
        border-radius: 6px;
        border: 1px solid #e8e8e8;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }

        &.active {
          border-color: #409eff;
          background: #f0f9ff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        }

        .group-content {
          padding: 12px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .group-info {
            display: flex;
            align-items: center;
            flex: 1;

            .group-icon {
              margin-right: 12px;
              color: #409eff;
              font-size: 18px;
            }

            .group-details {
              flex: 1;

              .group-name {
                font-size: 14px;
                color: #333;
                font-weight: 500;
                margin-bottom: 4px;
              }

              .group-desc {
                font-size: 12px;
                color: #999;
                line-height: 1.2;
              }
            }

            .device-count {
              margin-left: 12px;
            }
          }

          .group-actions {
            display: none;

            .el-button {
              padding: 4px;
              margin-left: 4px;
            }
          }
        }

        &:hover .group-actions {
          display: flex;
        }
      }
    }
  }

  .group-devices {
    border-top: 1px solid #f0f0f0;

    .devices-header {
      padding: 12px 16px;
      background: #fafafa;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h4 {
        margin: 0;
        font-size: 14px;
        color: #333;
      }
    }

    .devices-list {
      padding: 8px;

      .device-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        margin-bottom: 8px;
        background: #f9f9f9;
        border-radius: 6px;
        border: 1px solid #e8e8e8;
        cursor: move;
        transition: all 0.3s;

        &:hover {
          background: #f0f9ff;
          border-color: #409eff;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }

        .device-info {
          display: flex;
          align-items: center;
          flex: 1;

          .device-icon {
            margin-right: 8px;
            color: #67c23a;
          }

          .device-details {
            flex: 1;

            .device-name {
              font-size: 13px;
              color: #333;
              font-weight: 500;
              margin-bottom: 4px;
            }

            .device-meta {
              display: flex;
              flex-direction: column;
              gap: 2px;

              .device-no, .device-location, .device-resolution {
                font-size: 11px;
                color: #999;
              }

              .device-location {
                color: #666;
              }

              .device-resolution {
                color: #409eff;
                font-weight: 500;
              }
            }
          }

          .device-status {
            margin-left: 8px;
          }
        }

        .device-actions {
          display: none;

          .el-button {
            padding: 4px;
            margin-left: 4px;
          }
        }

        &:hover .device-actions {
          display: flex;
        }
      }
    }
  }
}

// 监控回放区域
.playback-box {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;

  .playback-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;

    .playback-header-left {
      h3 {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 600;
        color: white;
      }

      .playback-info {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }

  .time-filter-section {
    padding: 16px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;

    .time-filter-controls {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .quick-time-buttons {
        display: flex;
        align-items: center;
        gap: 12px;

        .filter-label {
          font-size: 14px;
          color: #666;
          white-space: nowrap;
          min-width: 80px;
        }

        .el-button-group {
          .el-button {
            padding: 6px 12px;
            font-size: 12px;
          }
        }
      }

      .custom-time-range {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;

        .filter-label {
          font-size: 14px;
          color: #666;
          white-space: nowrap;
          min-width: 80px;
        }
      }
    }
  }

  .playback-content {
    flex: 1;
    padding: 16px;
    overflow: auto;

    .el-table {
      border-radius: 6px;
      overflow: hidden;

      .el-table__header {
        background: #f8f9fa;
      }

      .el-table__row {
        &:hover {
          background: #f0f9ff;
        }
      }
    }
  }

  .no-device-selected {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
  }
}

// 设备选择相关样式
.device-item {
  position: relative;

  &.selected {
    background: #e6f7ff !important;
    border-color: #409eff !important;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3) !important;
  }

  .device-select-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    background: #409eff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
  }
}

.devices-header {
  .device-count-info {
    font-size: 12px;
    color: #999;
    margin-left: 8px;
  }
}

// 对话框样式
.add-device-content {
  .el-table {
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .time-filter-controls {
    .custom-time-range {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .filter-label {
        min-width: auto;
      }
    }
  }

  .playback-content {
    .el-table {
      font-size: 12px;
    }
  }
}

@media (max-width: 768px) {
  .preview {
    padding: 5px;
  }

  .group-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .devices-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .playback-header {
    padding: 12px;

    .playback-header-left {
      h3 {
        font-size: 14px;
      }

      .playback-info {
        font-size: 11px;
      }
    }
  }

  .time-filter-section {
    padding: 12px;

    .time-filter-controls {
      .quick-time-buttons {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .el-button-group {
          .el-button {
            padding: 4px 8px;
            font-size: 11px;
          }
        }
      }

      .custom-time-range {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .el-date-picker {
          width: 100% !important;
        }
      }
    }
  }

  .playback-content {
    padding: 8px;

    .el-table {
      font-size: 11px;

      .el-table__cell {
        padding: 8px 4px;
      }
    }
  }
}
</style>
