<template>
  <div class="test-page">
    <div class="page-header">
      <h1>能耗统计图表组件测试</h1>
      <p>这是一个测试页面，用于验证能耗统计图表组件的功能</p>
    </div>
    
    <div class="chart-section">
      <div class="chart-wrapper">
        <h2>能耗趋势图表</h2>
        <div class="chart-container">
          <Energyconsumption />
        </div>
      </div>
    </div>
    
    <div class="info-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card header="功能特性">
            <ul>
              <li>✅ 支持周视图（柱状图）和日视图（折线图）切换</li>
              <li>✅ 响应式设计，适配不同屏幕尺寸</li>
              <li>✅ 交互式图表，鼠标悬停显示详细数据</li>
              <li>✅ 图例说明，区分用电量和用水量</li>
              <li>✅ 数据单位显示（kWh、吨）</li>
              <li>✅ 加载状态和空状态处理</li>
            </ul>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card header="技术栈">
            <ul>
              <li><strong>Vue 3</strong> - 组合式API</li>
              <li><strong>ECharts 6.0</strong> - 图表库</li>
              <li><strong>Element Plus</strong> - UI组件库</li>
              <li><strong>SCSS</strong> - 样式预处理器</li>
            </ul>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <div class="data-section">
      <el-card header="数据格式示例">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="周数据格式" name="week">
            <pre><code>{{ weekDataExample }}</code></pre>
          </el-tab-pane>
          <el-tab-pane label="日数据格式" name="day">
            <pre><code>{{ dayDataExample }}</code></pre>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Energyconsumption from '@/components/wel/energyconsumption.vue'

const activeTab = ref('week')

const weekDataExample = `[
  { date: '周一', electricity: 120, water: 80 },
  { date: '周二', electricity: 132, water: 75 },
  { date: '周三', electricity: 101, water: 90 },
  { date: '周四', electricity: 134, water: 85 },
  { date: '周五', electricity: 90, water: 70 },
  { date: '周六', electricity: 230, water: 110 },
  { date: '周日', electricity: 210, water: 95 }
]`

const dayDataExample = `[
  { hour: '00:00', electricity: 45, water: 20 },
  { hour: '01:00', electricity: 42, water: 18 },
  { hour: '02:00', electricity: 40, water: 15 },
  // ... 其他小时数据
  { hour: '23:00', electricity: 75, water: 40 }
]`
</script>

<style scoped lang="scss">
.test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  
  .page-header {
    text-align: center;
    margin-bottom: 30px;
    
    h1 {
      color: #303133;
      margin-bottom: 10px;
    }
    
    p {
      color: #606266;
      font-size: 16px;
    }
  }
  
  .chart-section {
    margin-bottom: 30px;
    
    .chart-wrapper {
      background: #fff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      
      h2 {
        color: #303133;
        margin-bottom: 20px;
        text-align: center;
      }
      
      .chart-container {
        height: 400px;
      }
    }
  }
  
  .info-section {
    margin-bottom: 30px;
    
    :deep(.el-card) {
      .el-card__header {
        background-color: #f5f7fa;
        font-weight: 600;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 8px;
          color: #606266;
          
          strong {
            color: #303133;
          }
        }
      }
    }
  }
  
  .data-section {
    :deep(.el-card) {
      .el-card__header {
        background-color: #f5f7fa;
        font-weight: 600;
      }
      
      pre {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 15px;
        margin: 0;
        overflow-x: auto;
        
        code {
          color: #495057;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .test-page {
    padding: 15px;
    
    .chart-section {
      .chart-wrapper {
        padding: 15px;
        
        .chart-container {
          height: 300px;
        }
      }
    }
  }
}
</style>
