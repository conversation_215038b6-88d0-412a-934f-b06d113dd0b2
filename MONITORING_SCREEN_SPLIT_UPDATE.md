# 监控分屏切换功能更新说明

## 🎯 更新目标

为监控预览区域添加分屏数量切换功能，将头部布局从垂直改为水平布局，提供1分屏、4分屏、6分屏、9分屏四种显示模式。

## ✅ 完成的修改

### 1. 头部布局重构
**文件：** `src/views/kaihua/MonitoringManagement/preview.vue`

- ✅ 将监控预览头部改为左右水平布局
- ✅ 左侧保持原有标题和分组信息
- ✅ 右侧添加分屏切换控件

```vue
<div class="monitoring-header">
  <div class="monitoring-header-left">
    <h3>监控预览</h3>
    <div class="monitoring-info">当前分组信息</div>
  </div>
  <div class="monitoring-header-right">
    <div class="screen-split-controls">
      <span class="control-label">分屏数量：</span>
      <el-radio-group v-model="selectedScreenSplit">
        <el-radio-button :value="1">1分屏</el-radio-button>
        <el-radio-button :value="4">4分屏</el-radio-button>
        <el-radio-button :value="6">6分屏</el-radio-button>
        <el-radio-button :value="9">9分屏</el-radio-button>
      </el-radio-group>
    </div>
  </div>
</div>
```

### 2. 分屏切换功能
- ✅ 添加分屏数量选择（1/4/6/9分屏）
- ✅ 使用 `el-radio-group` 实现按钮组
- ✅ 默认选中9分屏模式
- ✅ 选中状态的视觉反馈

### 3. 动态网格布局
- ✅ 根据选择的分屏数量动态调整网格布局
- ✅ 支持四种网格配置：
  - 1分屏：1x1网格
  - 4分屏：2x2网格
  - 6分屏：3x2网格
  - 9分屏：3x3网格

### 4. 数据结构支持
```javascript
// 新增数据属性
selectedScreenSplit: 9, // 默认9分屏
screenSplitOptions: [
  { value: 1, label: '1分屏', cols: 1, rows: 1 },
  { value: 4, label: '4分屏', cols: 2, rows: 2 },
  { value: 6, label: '6分屏', cols: 3, rows: 2 },
  { value: 9, label: '9分屏', cols: 3, rows: 3 }
]

// 新增计算属性
displayDevices() {
  return this.currentGroupDevices.slice(0, this.selectedScreenSplit)
},
emptySlots() {
  return Math.max(0, this.selectedScreenSplit - this.currentGroupDevices.length)
}
```

### 5. 样式优化
- ✅ 专业的分屏切换按钮样式
- ✅ 不同网格布局的CSS类
- ✅ 悬停效果和过渡动画
- ✅ 设备状态徽章显示

### 6. 响应式设计
- ✅ 大屏幕：完整显示所有分屏选项
- ✅ 中等屏幕：头部垂直布局，限制最大分屏数
- ✅ 小屏幕：单列布局，按钮尺寸调整

## 🎨 界面效果

### 头部布局
```
┌─────────────────────────────────────────────────────────────┐
│ 监控预览                           分屏数量: [1][4][6][9]    │
│ 当前分组：大厅监控 (4个设备)                                │
└─────────────────────────────────────────────────────────────┘
```

### 网格布局示例

**1分屏 (1x1):**
```
┌─────────────┐
│   摄像头1   │
└─────────────┘
```

**4分屏 (2x2):**
```
┌──────┬──────┐
│摄像头1│摄像头2│
├──────┼──────┤
│摄像头3│摄像头4│
└──────┴──────┘
```

**6分屏 (3x2):**
```
┌────┬────┬────┐
│摄像头1│摄像头2│摄像头3│
├────┼────┼────┤
│摄像头4│摄像头5│摄像头6│
└────┴────┴────┘
```

**9分屏 (3x3):**
```
┌────┬────┬────┐
│摄像头1│摄像头2│摄像头3│
├────┼────┼────┤
│摄像头4│摄像头5│摄像头6│
├────┼────┼────┤
│摄像头7│摄像头8│摄像头9│
└────┴────┴────┘
```

## 🔧 技术特点

### 1. 动态网格系统
- 使用CSS Grid实现灵活的网格布局
- 动态类名切换：`grid-1x1`, `grid-2x2`, `grid-3x2`, `grid-3x3`
- 自适应容器高度和间距

### 2. 智能设备显示
- 根据分屏数量限制显示的设备数量
- 自动计算和填充空白格子
- 设备状态实时显示

### 3. 用户体验优化
- 平滑的切换动画
- 直观的按钮选中状态
- 响应式布局适配

## 📱 响应式适配

### 桌面端 (>1200px)
- 水平头部布局
- 完整分屏选项
- 最佳视觉效果

### 平板端 (768px-1200px)
- 垂直头部布局
- 限制最大分屏数
- 适当调整按钮尺寸

### 移动端 (<768px)
- 紧凑的垂直布局
- 单列网格显示
- 小尺寸按钮和文字

## 🎉 更新完成

监控分屏切换功能已成功添加，提供了专业的监控预览体验。用户可以根据需要选择不同的分屏模式，系统会自动调整网格布局和设备显示，确保在各种屏幕尺寸下都能获得最佳的使用体验。
